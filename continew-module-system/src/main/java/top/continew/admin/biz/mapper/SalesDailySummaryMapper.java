package top.continew.admin.biz.mapper;

import org.apache.ibatis.annotations.Param;
import top.continew.admin.biz.model.entity.SalesDailySummaryDO;
import top.continew.admin.common.config.mybatis.DataPermissionMapper;

import java.time.LocalDate;
import java.util.List;

/**
 * 商务日报 Mapper
 *
 * <AUTHOR>
 * @since 2025/08/01 14:29
 */
public interface SalesDailySummaryMapper extends DataPermissionMapper<SalesDailySummaryDO> {

    /**
     * 查询用户重复日期的日报
     *
     * @param userId 用户ID
     * @return 重复日期列表
     */
    List<LocalDate> selectDuplicateDates(@Param("userId") Long userId);

}