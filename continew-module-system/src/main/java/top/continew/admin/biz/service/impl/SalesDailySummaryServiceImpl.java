package top.continew.admin.biz.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import jakarta.servlet.http.HttpServletResponse;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import top.continew.admin.biz.mapper.SalesDailySummaryMapper;
import top.continew.admin.biz.model.entity.SalesDailySummaryDO;
import top.continew.admin.biz.model.query.SalesDailySummaryQuery;
import top.continew.admin.biz.model.req.SalesDailySummaryReq;
import top.continew.admin.biz.model.resp.SalesDailySummaryDetailResp;
import top.continew.admin.biz.model.resp.SalesDailySummaryResp;
import top.continew.admin.biz.service.SalesDailySummaryService;
import top.continew.starter.core.exception.BusinessException;
import top.continew.starter.extension.crud.model.query.PageQuery;
import top.continew.starter.extension.crud.model.query.SortQuery;
import top.continew.starter.extension.crud.model.resp.PageResp;
import top.continew.starter.extension.crud.service.BaseServiceImpl;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 商务日报业务实现
 *
 * <AUTHOR>
 * @since 2025/08/01 14:29
 */
@Service
@RequiredArgsConstructor
public class SalesDailySummaryServiceImpl extends BaseServiceImpl<SalesDailySummaryMapper, SalesDailySummaryDO, SalesDailySummaryResp, SalesDailySummaryDetailResp, SalesDailySummaryQuery, SalesDailySummaryReq> implements SalesDailySummaryService {

    @Override
    public void telegramSave(SalesDailySummaryDO salesDailySummaryDO) {


        // 计算截止时间：记录日期 + 1天 + 2小时
        LocalDateTime deadline = salesDailySummaryDO.getRecordDate().atTime(2, 0).plusDays(1);
        // 设置创建时间为当前时间（如果未设置）
        if (salesDailySummaryDO.getCreateTime() == null) {
            salesDailySummaryDO.setCreateTime(LocalDateTime.now());
        }
        // 判断是否超过截止时间
        boolean isLate = salesDailySummaryDO.getCreateTime().isAfter(deadline);
        salesDailySummaryDO.setIsLateSubmission(isLate);

        save(salesDailySummaryDO);
    }

    @Override
    public List<LocalDate> getDuplicateDates(Long userId) {
        return baseMapper.selectDuplicateDates(userId);
    }
}