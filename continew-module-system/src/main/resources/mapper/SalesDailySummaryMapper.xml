<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="top.continew.admin.biz.mapper.SalesDailySummaryMapper">

    <!-- 查询用户重复日期的日报 -->
    <select id="selectDuplicateDates" resultType="java.time.LocalDate">
        SELECT record_date
        FROM biz_sales_daily_summary
        WHERE create_user = #{userId}
        GROUP BY record_date
        HAVING COUNT(*) > 1
        ORDER BY record_date DESC
    </select>

</mapper>